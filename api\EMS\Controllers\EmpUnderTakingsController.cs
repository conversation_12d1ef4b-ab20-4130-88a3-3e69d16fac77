﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EMS.Data;
using EMS.Models;

namespace EMS.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmpUnderTakingsController : ControllerBase
    {
        private readonly EMSDbContext _context;

        public EmpUnderTakingsController(EMSDbContext context)
        {
            _context = context;
        }

        // GET: api/EmpUnderTakings
        [HttpGet]
        public async Task<ActionResult<IEnumerable<EmpUnderTaking>>> GetEmpUnderTakings()
        {
            return await _context.EmpUnderTakings.ToListAsync();
        }

        // GET: api/EmpUnderTakings/5
        [HttpGet("{id}")]
        public async Task<ActionResult<EmpUnderTaking>> GetEmpUnderTaking(int id)
        {
            var empUnderTaking = await _context.EmpUnderTakings.FindAsync(id);

            if (empUnderTaking == null)
            {
                return NotFound();
            }

            return empUnderTaking;
        }

        // PUT: api/EmpUnderTakings/5
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPut("{id}")]
        public async Task<IActionResult> PutEmpUnderTaking(int id, EmpUnderTaking empUnderTaking)
        {
            if (id != empUnderTaking.EmpUnderTakingID)
            {
                return BadRequest();
            }

            _context.Entry(empUnderTaking).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EmpUnderTakingExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/EmpUnderTakings
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPost]
        public async Task<ActionResult<EmpUnderTaking>> PostEmpUnderTaking(EmpUnderTaking empUnderTaking)
        {
            _context.EmpUnderTakings.Add(empUnderTaking);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetEmpUnderTaking", new { id = empUnderTaking.EmpUnderTakingID }, empUnderTaking);
        }

        // DELETE: api/EmpUnderTakings/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEmpUnderTaking(int id)
        {
            var empUnderTaking = await _context.EmpUnderTakings.FindAsync(id);
            if (empUnderTaking == null)
            {
                return NotFound();
            }

            _context.EmpUnderTakings.Remove(empUnderTaking);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool EmpUnderTakingExists(int id)
        {
            return _context.EmpUnderTakings.Any(e => e.EmpUnderTakingID == id);
        }
    }
}
