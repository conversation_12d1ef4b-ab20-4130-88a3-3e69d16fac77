﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EMS.Models
{
    public class EmpCred
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int EmpCredID { get; set; } // Unique identifier for the employee credit record

        public int EmpID { get; set; } // Foreign key to the Employee table

        public string UserName { get; set; } // Username of the employee

        public string Password { get; set; } // Password for the employee's account

        public bool IsAutoGenerated { get; set; } = true;    
    }
}
