﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EMS.Data;
using EMS.Models;

namespace EMS.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmpAcadDetailsController : ControllerBase
    {
        private readonly EMSDbContext _context;

        public EmpAcadDetailsController(EMSDbContext context)
        {
            _context = context;
        }

        // GET: api/EmpAcadDetails
        [HttpGet]
        public async Task<ActionResult<IEnumerable<EmpAcadDetail>>> GetEmpAcadDetails()
        {
            return await _context.EmpAcadDetails.ToListAsync();
        }

        // GET: api/EmpAcadDetails/5
        [HttpGet("{id}")]
        public async Task<ActionResult<IEnumerable<EmpAcadDetail>>> GetEmpAcadDetail(int id)
        {
            var empAcadDetail = await _context.EmpAcadDetails.Where(ead=>ead.EmpID == id).ToListAsync();

            if (empAcadDetail == null)
            {
                return NotFound();
            }

            return empAcadDetail;
        }

        // PUT: api/EmpAcadDetails/5
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPut("{id}")]
        public async Task<IActionResult> PutEmpAcadDetail(int id, EmpAcadDetail empAcadDetail)
        {
            if (id != empAcadDetail.EadID)
            {
                return BadRequest();
            }

            _context.Entry(empAcadDetail).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EmpAcadDetailExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/EmpAcadDetails
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPost]
        public async Task<ActionResult<EmpAcadDetail>> PostEmpAcadDetail(EmpAcadDetail empAcadDetail)
        {
            _context.EmpAcadDetails.Add(empAcadDetail);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetEmpAcadDetail", new { id = empAcadDetail.EadID }, empAcadDetail);
        }

        // DELETE: api/EmpAcadDetails/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEmpAcadDetail(int id)
        {
            var empAcadDetail = await _context.EmpAcadDetails.FindAsync(id);
            if (empAcadDetail == null)
            {
                return NotFound();
            }

            _context.EmpAcadDetails.Remove(empAcadDetail);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool EmpAcadDetailExists(int id)
        {
            return _context.EmpAcadDetails.Any(e => e.EadID == id);
        }
    }
}
