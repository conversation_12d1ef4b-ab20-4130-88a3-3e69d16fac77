import API from '@/services/API';

const createbank = async (bankData) => {
  try {
    const bankPayload = bankData

    console.log('Creating bank with payload:', bankPayload);

    const bankResponse = await API.post('/EmpBankDetails', bankPayload);

    if (!bankResponse.data) {
      throw new Error('Failed to create bank - no response data');
    }

    const createdbank = bankResponse.data;

    console.log('bank created successfully:', createdbank);
    return createdbank;

  } catch (error) {
    console.error('Error in createbank service:', error);

    if (error.response) {
      const statusCode = error.response.status;
      const errorMessage = error.response.data?.message || error.response.data || 'Unknown server error';
      throw new Error(`Server error (${statusCode}): ${errorMessage}`);
    } else if (error.request) {
      throw new Error('Network error: Unable to connect to server');
    } else {
      throw new Error(error.message || 'An unexpected error occurred');
    }
  }
};

const importBankDetails = async (bankDetailsArray) => {
  try {
    // Transform the data to match API format
    const transformedData = bankDetailsArray.map(item => ({
      EmpID: parseInt(item.empID),
      BankName: item.bankName,
      AccountNumber: item.accountNumber,
      IFSCCode: item.ifscCode,
      BranchName: item.branchName,
      CancelledCheque: null // Optional for now
    }));

    console.log('Importing bank details with payload:', transformedData);

    const response = await API.post('/EmpBankDetails', transformedData);

    if (!response.data) {
      throw new Error('Failed to import bank details - no response data');
    }

    console.log('Bank details imported successfully:', response.data);
    return response.data;

  } catch (error) {
    console.error('Error in importBankDetails service:', error);

    if (error.response) {
      const statusCode = error.response.status;
      const errorMessage = error.response.data?.message || error.response.data || 'Unknown server error';
      throw new Error(`Server error (${statusCode}): ${errorMessage}`);
    } else if (error.request) {
      throw new Error('Network error: Unable to connect to server');
    } else {
      throw new Error(error.message || 'An unexpected error occurred');
    }
  }
};

const BankService = {
  createbank,
  importBankDetails,
};

export default BankService;