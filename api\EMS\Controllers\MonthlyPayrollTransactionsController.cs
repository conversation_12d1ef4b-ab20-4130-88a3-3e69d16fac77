﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EMS.Data;
using EMS.Models;

namespace EMS.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MonthlyPayrollTransactionsController : ControllerBase
    {
        private readonly EMSDbContext _context;

        public MonthlyPayrollTransactionsController(EMSDbContext context)
        {
            _context = context;
        }

        // GET: api/MonthlyPayrollTransactions
        [HttpGet]
        public async Task<ActionResult<IEnumerable<MonthlyPayrollTransaction>>> GetMonthlyPayrollTransactions()
        {
            return await _context.MonthlyPayrollTransactions.ToListAsync();
        }

        // GET: api/MonthlyPayrollTransactions/5
        [HttpGet("{id}")]
        public async Task<ActionResult<MonthlyPayrollTransaction>> GetMonthlyPayrollTransaction(int id)
        {
            var monthlyPayrollTransaction = await _context.MonthlyPayrollTransactions.FindAsync(id);

            if (monthlyPayrollTransaction == null)
            {
                return NotFound();
            }

            return monthlyPayrollTransaction;
        }

        // PUT: api/MonthlyPayrollTransactions/5
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPut("{id}")]
        public async Task<IActionResult> PutMonthlyPayrollTransaction(int id, MonthlyPayrollTransaction monthlyPayrollTransaction)
        {
            if (id != monthlyPayrollTransaction.MptID)
            {
                return BadRequest();
            }

            _context.Entry(monthlyPayrollTransaction).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!MonthlyPayrollTransactionExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }


        [HttpPost]
        public async Task<ActionResult<object>> PostMonthlyPayrollTransaction(string Month, string Year)
        {
            // 1. Get all active employees
            var allEmployees = await _context.Employees
                .Where(e => e.OBStatus == false && e.IsActive == true)
                .ToListAsync();

            // 2. Get hourly employees (EmpTypeID == 2)
            var hourlyEmployeePositions = await _context.EmpDeptPositions
                .Where(edp => edp.EmpTypeID == 2)
                .ToListAsync();

            var hourlyEmployeeIds = hourlyEmployeePositions.Select(edp => edp.EmpID).ToHashSet();

            // 3. Get fixed employees (active but not hourly)
            var fixedEmployees = allEmployees
                .Where(emp => !hourlyEmployeeIds.Contains(emp.EmpID))
                .ToList();

            // 4. Convert month/year to integers
            int month = int.Parse(Month);
            int year = int.Parse(Year);

            // 5. Get attendance records for the month
            var attendanceRecords = await _context.Attendances
                .Where(a => a.Date.HasValue && a.Date.Value.Month == month && a.Date.Value.Year == year)
                .ToListAsync();

            // 6. Filter attendance only for hourly employees
            var filteredAttendance = attendanceRecords
                .Where(a => hourlyEmployeeIds.Contains(a.EmpID))
                .ToList();

            // 7. Calculate working hours per hourly employee
            var employeeWorkingHours = filteredAttendance
                .GroupBy(a => a.EmpID)
                .ToDictionary(
                    g => g.Key,
                    g => new TimeSpan(g.Sum(a => a.TotalHours.HasValue ? a.TotalHours.Value.Ticks : 0))
                );

            // 8. Fetch payroll data for all employees
            var payroll = await _context.EmpCTCandPayrolls.ToListAsync();

            var transactionsToAdd = new List<MonthlyPayrollTransaction>();

            // 9. Process hourly employees
            foreach (var emp in employeeWorkingHours)
            {
                var empId = emp.Key;
                var totalTime = emp.Value;
                var payrollData = payroll.FirstOrDefault(p => p.EmpID == empId);

                if (payrollData == null)
                    continue;

                // Step 1: Actual Hours (M)
                int totalHours = totalTime.Minutes >= 30
                    ? (int)totalTime.TotalHours + 1
                    : (int)totalTime.TotalHours;

                // Step 2: Salary (K) and Hourly Rate (L)
                decimal basicSalary = payrollData.BasicSalary;
                decimal hourlyRate = Math.Round(basicSalary / 240, 0);

                // Step 3: Apply Formula (Excel logic)
                decimal netSalaryPayment = 0;
                if (totalHours >= 600)
                    netSalaryPayment = (basicSalary * 3) + ((totalHours - 600) * hourlyRate);
                else if (totalHours >= 400)
                    netSalaryPayment = (basicSalary * 2) + ((totalHours - 400) * hourlyRate);
                else if (totalHours > 200)
                    netSalaryPayment = (basicSalary * 1) + ((totalHours - 200) * hourlyRate);
                else if (totalHours > 189)
                    netSalaryPayment = basicSalary;
                else if (totalHours >= 150)
                    netSalaryPayment = basicSalary - ((200 - totalHours) * hourlyRate);
                else // totalHours < 150
                    netSalaryPayment = hourlyRate * totalHours;

                // Step 4: Statutory Deductions
                decimal esi = 0;
                if (payrollData.EsiApplicable && netSalaryPayment <= 21000)
                    esi = Math.Round(netSalaryPayment * 0.0075m, 2);

                decimal epf = 0;
                if (payrollData.EpfApplicable)
                    epf = Math.Round(basicSalary * 0.12m, 2);

                // Step 5: Create Payroll Transaction
                var transaction = new MonthlyPayrollTransaction
                {
                    EmpID = empId,
                    Month = Month,
                    Year = year,
                    BasicSalary = payrollData.BasicSalary,
                    HRA = payrollData.Hra,
                    DA = payrollData.Da,
                    OtherAllowances = payrollData.SpecialAllowance,
                    Deductions = Math.Round((epf + esi), 0)
                };

                Console.WriteLine(netSalaryPayment);
                Console.WriteLine(transaction.HRA);
                Console.WriteLine(transaction.DA);
                Console.WriteLine(transaction.OtherAllowances);
                Console.WriteLine(transaction.Deductions);

                // NetPay = NetSalaryPayment (formula) + Allowances – (Deductions + EPF + ESI)
                transaction.NetPay = Math.Round(
    netSalaryPayment
    + (transaction.HRA ?? 0)
    + (transaction.DA ?? 0)
    + (transaction.OtherAllowances ?? 0)
    - epf
    - esi, 0);

                transactionsToAdd.Add(transaction);
            }

            // 10. Process fixed employees
            foreach (var emp in fixedEmployees)
            {
                var payrollData = payroll.FirstOrDefault(p => p.EmpID == emp.EmpID);

                if (payrollData == null)
                    continue;

                decimal basicSalary = payrollData.BasicSalary;

                decimal gross = basicSalary
    + payrollData.Hra
    + payrollData.Da
    + payrollData.SpecialAllowance;

                gross = Math.Round(gross, 2);

                // Statutory deductions
                decimal esi = 0;
                if (payrollData.EsiApplicable && gross <= 21000)
                    esi = Math.Round(gross * 0.0075m, 2);

                decimal epf = 0;
                if (payrollData.EpfApplicable)
                    epf = Math.Round(basicSalary * 0.12m, 2);

                var transaction = new MonthlyPayrollTransaction
                {
                    EmpID = emp.EmpID,
                    Month = Month,
                    Year = year,
                    BasicSalary = payrollData.BasicSalary,
                    HRA = payrollData.Hra,
                    DA = payrollData.Da,
                    OtherAllowances = payrollData.SpecialAllowance,
                    Deductions = Math.Round((epf + esi), 0)

                };

                transaction.NetPay = Math.Round(
    gross
    - epf
    - esi, 0);

                transactionsToAdd.Add(transaction);
            }

            // 11. Save all transactions to the database
            _context.MonthlyPayrollTransactions.AddRange(transactionsToAdd);
            await _context.SaveChangesAsync();

            return Ok(new { Message = $"{transactionsToAdd.Count} payroll transactions created successfully." });
        }



        // DELETE: api/MonthlyPayrollTransactions/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteMonthlyPayrollTransaction(int id)
        {
            var monthlyPayrollTransaction = await _context.MonthlyPayrollTransactions.FindAsync(id);
            if (monthlyPayrollTransaction == null)
            {
                return NotFound();
            }

            _context.MonthlyPayrollTransactions.Remove(monthlyPayrollTransaction);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool MonthlyPayrollTransactionExists(int id)
        {
            return _context.MonthlyPayrollTransactions.Any(e => e.MptID == id);
        }
    }
}
