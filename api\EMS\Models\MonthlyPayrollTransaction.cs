﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class MonthlyPayrollTransaction
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int MptID { get; set; }

    public int EmpID { get; set; }

    public string Month { get; set; } // e.g., "January"

    public int Year { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? BasicSalary { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? HRA { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? DA { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? OtherAllowances { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? Deductions { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? NetPay { get; set; }


    public DateTime TransactionDate { get; set; } = DateTime.Now;
}
