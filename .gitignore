# Frontend (React/Vite)
ui/node_modules/
ui/dist/
ui/build/
ui/uploads/
ui/.env
ui/.env.local
ui/.env.development.local
ui/.env.test.local
ui/.env.production.local

# General Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# .NET API
api/.vs/
api/EMS/bin/
api/EMS/obj/
api/EMS/wwwroot/
api/EMS/Properties/launchSettings.json

# .NET Build results
api/**/bin/
api/**/obj/
api/**/*.user
api/**/*.suo
api/**/*.userosscache
api/**/*.sln.docstates

# Visual Studio files
api/.vs/
api/**/.vs/
api/**/*.vscode/
api/**/*.swp
api/**/*.swo
api/**/*~

# .NET Core
api/**/project.lock.json
api/**/project.fragment.lock.json
api/**/artifacts/

# Entity Framework
api/**/Migrations/*.Designer.cs
api/**/Migrations/*_*.cs
!api/**/Migrations/EMSDbContextModelSnapshot.cs

# Configuration files (keep templates, ignore actual config)
api/**/appsettings.Development.json
api/**/appsettings.Production.json
api/**/appsettings.Local.json
api/**/connectionstrings.json

# Logs
api/**/*.log
api/**/logs/

# Database files
api/**/*.db
api/**/*.sqlite
api/**/*.sqlite3

# Temporary files
api/**/*.tmp
api/**/*.temp
api/**/Thumbs.db
api/**/.DS_Store

# NuGet
api/**/packages/
api/**/*.nupkg
api/**/*.snupkg
api/**/NuGet.Config

# Test results
api/**/TestResults/
api/**/*.trx
api/**/*.coverage
api/**/*.coveragexml

# IDE files
.idea/
*.iml
*.ipr
*.iws
