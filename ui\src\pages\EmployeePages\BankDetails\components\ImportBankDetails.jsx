import ExcelImportService from '@/services/ExcelMappingService';
import React, { useState, useCallback } from 'react';
import BankService from '@/services/Bank/bank';
import { Upload, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

const ImportBankDetails = () => {
    const [myData, setMyData] = useState([]);
    const [isUploading, setIsUploading] = useState(false);
    const [uploadStatus, setUploadStatus] = useState(null); 
    const [uploadMessage, setUploadMessage] = useState('');

    const predefinedFields = [
        { key: 'empID', label: 'Employee ID', required: true, variations: ['employee id', 'Employee Id', 'EmployeeID', 'Empid', 'empid', 'EMPLOYEE ID', 'EMPLOYEE ID', 'EmpID'] },
        { key: 'bankName', label: 'Bank Name', required: true, variations: ['bank name', 'Bank Name', 'Bank', 'Name', 'bank', 'names', 'name', 'BANK NAME', 'NAMES'] },
        { key: 'accountNumber', label: 'Account Number', required: true, variations: ['account number', 'Account Number', 'Account', 'Number', 'account', 'number', 'ACCOUNT NUMBER', 'ACCOUNT NUMBER', 'AC No.'] },
        { key: 'ifscCode', label: 'IFSC Code', required: true, variations: ['ifsc code', 'IFSC Code', 'IFSC', 'Code', 'ifsc', 'code', 'IFSC CODE', 'IFSC CODE'] },
        { key: 'branchName', label: 'Branch Name', required: true, variations: ['branch name', 'Branch Name', 'Branch', 'Name', 'branch', 'name', 'BRANCH NAME', 'BRANCH NAME'] }
    ];

    const handleDataChange = useCallback((newData) => {
        setMyData(newData);
        setUploadStatus(null); // Reset status when new data is loaded
        setUploadMessage('');
        console.log('Received Data:', newData);
    }, []);

    const handleUpload = async () => {
        if (!myData || myData.length === 0) {
            setUploadStatus('error');
            setUploadMessage('No data to upload');
            return;
        }

        setIsUploading(true);
        setUploadStatus(null);
        setUploadMessage('');

        try {
            const result = await BankService.importBankDetails(myData);
            setUploadStatus('success');
            setUploadMessage(`Successfully uploaded ${myData.length} bank details`);
            console.log('Upload successful:', result);
        } catch (error) {
            setUploadStatus('error');
            setUploadMessage(error.message || 'Failed to upload bank details');
            console.error('Upload failed:', error);
        } finally {
            setIsUploading(false);
        }
    };
    return (
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <ExcelImportService
                predefinedFields={predefinedFields}
                onDataChange={handleDataChange}
                debugMode={false}
                exportJsonEnabled={false}
            />

            {/* Upload Section */}
            {myData && myData.length > 0 && (
                <div className="border-t border-gray-200 p-4 bg-gray-50">
                    <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-600">
                            {myData.length} bank detail{myData.length !== 1 ? 's' : ''} ready to upload
                        </div>
                        <button
                            onClick={handleUpload}
                            disabled={isUploading}
                            className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                                isUploading
                                    ? 'bg-gray-400 text-white cursor-not-allowed'
                                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                            }`}
                        >
                            {isUploading ? (
                                <>
                                    <Loader2 className="w-4 h-4 animate-spin" />
                                    Uploading...
                                </>
                            ) : (
                                <>
                                    <Upload className="w-4 h-4" />
                                    Upload Bank Details
                                </>
                            )}
                        </button>
                    </div>

                    {/* Status Message */}
                    {uploadStatus && (
                        <div className={`mt-3 p-3 rounded-md text-sm flex items-center gap-2 ${
                            uploadStatus === 'success'
                                ? 'bg-green-50 text-green-700 border border-green-200'
                                : 'bg-red-50 text-red-700 border border-red-200'
                        }`}>
                            {uploadStatus === 'success' ? (
                                <CheckCircle className="w-4 h-4" />
                            ) : (
                                <AlertCircle className="w-4 h-4" />
                            )}
                            {uploadMessage}
                        </div>
                    )}
                </div>
            )}
        </div>
    )
}

export default ImportBankDetails