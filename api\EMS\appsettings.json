{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    //"DefaultAppDBConnection": "server=localhost;port=3306;database=EMS;user=root;password=***********" ,
    //"DefaultAppDBConnection": "server=localhost;port=3306;database=EMS;user=root;password=******",
    //"DefaultAppDBConnection": "server=localhost;port=3306;database=EMS;user=root;password=****",
    "DefaultAppDBConnection": "server=************;port=3306;database=EMS;user=VM5;password=**********"
  },

  "EmailSettings": {
    "Email": "<EMAIL>",
    "Password": "svpkyvjwkdyvqlol",
    "Host": "smtp.gmail.com",
    "Displayname": "<PERSON><PERSON>App",
    "Port": 587
  },

  "Jwt": {
    "Issuer": "https://localhost:7247/",
    "Audience": "https://localhost:7247/",
    "Key": "your-secret-key-of-at-least-32-characters-length-for-256-bits",
    "ExpireTime": "480"
  }
}
