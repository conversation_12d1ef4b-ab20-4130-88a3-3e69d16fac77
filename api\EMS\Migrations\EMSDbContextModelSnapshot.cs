﻿// <auto-generated />
using System;
using EMS.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace EMS.Migrations
{
    [DbContext(typeof(EMSDbContext))]
    partial class EMSDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.18")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            MySqlModelBuilderExtensions.AutoIncrementColumns(modelBuilder);

            modelBuilder.Entity("EMS.Models.Attendance", b =>
                {
                    b.Property<int>("AttendaceID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("AttendaceID"));

                    b.Property<TimeOnly?>("CheckInTime")
                        .HasColumnType("time(6)");

                    b.Property<TimeOnly?>("CheckOutTime")
                        .HasColumnType("time(6)");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("EmpID")
                        .HasColumnType("int");

                    b.Property<TimeSpan?>("TotalHours")
                        .HasColumnType("time(6)");

                    b.HasKey("AttendaceID");

                    b.ToTable("Attendances");
                });

            modelBuilder.Entity("EMS.Models.Department", b =>
                {
                    b.Property<int>("DeptID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("DeptID"));

                    b.Property<string>("DeptName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("DeptID");

                    b.ToTable("Departments");
                });

            modelBuilder.Entity("EMS.Models.Docs", b =>
                {
                    b.Property<int>("DocID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("DocID"));

                    b.Property<string>("DocName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<bool>("isDocIDNumRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("isRequired")
                        .HasColumnType("tinyint(1)");

                    b.HasKey("DocID");

                    b.ToTable("Docs");
                });

            modelBuilder.Entity("EMS.Models.EmpAcadDetail", b =>
                {
                    b.Property<int>("EadID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("EadID"));

                    b.Property<string>("Degree")
                        .HasColumnType("varchar(255)");

                    b.Property<int>("EmpID")
                        .HasColumnType("int");

                    b.Property<string>("Grade")
                        .HasColumnType("longtext");

                    b.Property<string>("Institution")
                        .HasColumnType("longtext");

                    b.Property<string>("YearOfPassing")
                        .HasColumnType("longtext");

                    b.HasKey("EadID");

                    b.HasIndex("EmpID", "Degree")
                        .IsUnique();

                    b.ToTable("EmpAcadDetails");
                });

            modelBuilder.Entity("EMS.Models.EmpAssets", b =>
                {
                    b.Property<int>("EmpAssetID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("EmpAssetID"));

                    b.Property<string>("AssetName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("EmpID")
                        .HasColumnType("int");

                    b.Property<bool>("IsReceived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsReturned")
                        .HasColumnType("tinyint(1)");

                    b.HasKey("EmpAssetID");

                    b.ToTable("EmpAssets");
                });

            modelBuilder.Entity("EMS.Models.EmpBankDetail", b =>
                {
                    b.Property<int>("EbdID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("EbdID"));

                    b.Property<string>("AccountNumber")
                        .HasColumnType("longtext");

                    b.Property<string>("BankName")
                        .HasColumnType("longtext");

                    b.Property<string>("BranchName")
                        .HasColumnType("longtext");

                    b.Property<string>("CancelledCheque")
                        .HasColumnType("longtext");

                    b.Property<int>("EmpID")
                        .HasColumnType("int");

                    b.Property<string>("IFSCCode")
                        .HasColumnType("longtext");

                    b.HasKey("EbdID");

                    b.ToTable("EmpBankDetails");
                });

            modelBuilder.Entity("EMS.Models.EmpCTCandPayroll", b =>
                {
                    b.Property<int>("EmpCtcID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("EmpCtcID"));

                    b.Property<decimal>("BasicSalary")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("CtcAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Da")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("EffectiveDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("EmpID")
                        .HasColumnType("int");

                    b.Property<bool>("EpfApplicable")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("EsiApplicable")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("GrossSalary")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("HourlyRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Hra")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsHourlyEmployee")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("Lta")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MonthlyBasicPay")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("NetAnnualSalary")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("NetMonthlyPayable")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PerformanceBonus")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("ProfessionalTaxApplicable")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("SpecialAllowance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalDeductions")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("WorkingHours")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("EmpCtcID");

                    b.ToTable("EmpCTCandPayrolls");
                });

            modelBuilder.Entity("EMS.Models.EmpCred", b =>
                {
                    b.Property<int>("EmpCredID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("EmpCredID"));

                    b.Property<int>("EmpID")
                        .HasColumnType("int");

                    b.Property<bool>("IsAutoGenerated")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("EmpCredID");

                    b.ToTable("EmpCreds");
                });

            modelBuilder.Entity("EMS.Models.EmpDeptPos", b =>
                {
                    b.Property<int>("EdpID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("EdpID"));

                    b.Property<int>("DeptID")
                        .HasColumnType("int");

                    b.Property<int>("EmpID")
                        .HasColumnType("int");

                    b.Property<int?>("EmpTypeID")
                        .HasColumnType("int");

                    b.Property<int>("PositionID")
                        .HasColumnType("int");

                    b.HasKey("EdpID");

                    b.ToTable("EmpDeptPositions");
                });

            modelBuilder.Entity("EMS.Models.EmpDoc", b =>
                {
                    b.Property<int>("EmpDocId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("EmpDocId"));

                    b.Property<string>("DocIDNumber")
                        .HasColumnType("longtext");

                    b.Property<string>("DocName")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("DocPath")
                        .HasColumnType("longtext");

                    b.Property<int>("EmpId")
                        .HasColumnType("int");

                    b.HasKey("EmpDocId");

                    b.HasIndex("EmpId", "DocName")
                        .IsUnique();

                    b.ToTable("EmpDocs");
                });

            modelBuilder.Entity("EMS.Models.EmpProfDetail", b =>
                {
                    b.Property<int>("EpdID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("EpdID"));

                    b.Property<int>("EmpID")
                        .HasColumnType("int");

                    b.Property<string>("PreviousCompany")
                        .HasColumnType("longtext");

                    b.Property<string>("PreviousExperience")
                        .HasColumnType("longtext");

                    b.Property<string>("PreviousExperienceCeritificate")
                        .HasColumnType("longtext");

                    b.Property<string>("PreviousPosition")
                        .HasColumnType("longtext");

                    b.HasKey("EpdID");

                    b.ToTable("EmpProfDetails");
                });

            modelBuilder.Entity("EMS.Models.EmpType", b =>
                {
                    b.Property<int>("EmpTypeID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("EmpTypeID"));

                    b.Property<string>("EmpTypeName")
                        .HasColumnType("longtext");

                    b.HasKey("EmpTypeID");

                    b.ToTable("EmpTypes");
                });

            modelBuilder.Entity("EMS.Models.EmpUnderTaking", b =>
                {
                    b.Property<int>("EmpUnderTakingID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("EmpUnderTakingID"));

                    b.Property<int>("EmpID")
                        .HasColumnType("int");

                    b.Property<string>("UndertakingFilePath")
                        .HasColumnType("longtext");

                    b.Property<string>("UndertakingText")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("UndertakingType")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("EmpUnderTakingID");

                    b.ToTable("EmpUnderTakings");
                });

            modelBuilder.Entity("EMS.Models.Employee", b =>
                {
                    b.Property<int>("EmpID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("EmpID"));

                    b.Property<string>("Address")
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("DateOfJoining")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("EmergencyContact")
                        .HasColumnType("longtext");

                    b.Property<string>("EmergencyContactNumber")
                        .HasColumnType("longtext");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("Gender")
                        .HasColumnType("longtext");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LastName")
                        .HasColumnType("longtext");

                    b.Property<int?>("LocationID")
                        .HasColumnType("int");

                    b.Property<bool>("OBStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("PersonalEmail")
                        .IsRequired()
                        .HasColumnType("varchar(255)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(10)
                        .HasColumnType("varchar(10)");

                    b.Property<string>("ProfilePicture")
                        .HasColumnType("longtext");

                    b.Property<int>("RoleID")
                        .HasColumnType("int");

                    b.Property<string>("Signature")
                        .HasColumnType("longtext");

                    b.Property<string>("WorkEmail")
                        .HasColumnType("longtext");

                    b.HasKey("EmpID");

                    b.HasIndex("PersonalEmail")
                        .IsUnique();

                    b.ToTable("Employees");
                });

            modelBuilder.Entity("EMS.Models.Leave", b =>
                {
                    b.Property<int>("LeaveID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("LeaveID"));

                    b.Property<int?>("ApprovedbyEmpID")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("EmpID")
                        .HasColumnType("int");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime(6)");

                    b.Property<bool?>("IsApproved")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LeaveReason")
                        .HasColumnType("longtext");

                    b.Property<string>("LeaveType")
                        .HasColumnType("longtext");

                    b.Property<string>("RejectionReason")
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime(6)");

                    b.HasKey("LeaveID");

                    b.ToTable("Leaves");
                });

            modelBuilder.Entity("EMS.Models.LeaveBalance", b =>
                {
                    b.Property<int>("LeaveBalanceID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("LeaveBalanceID"));

                    b.Property<int>("Adjusted")
                        .HasColumnType("int");

                    b.Property<int>("Availed")
                        .HasColumnType("int");

                    b.Property<int>("ClosingBalance")
                        .HasColumnType("int");

                    b.Property<int>("EmpID")
                        .HasColumnType("int");

                    b.Property<int>("LeaveMasterID")
                        .HasColumnType("int");

                    b.Property<int>("OpeningBalance")
                        .HasColumnType("int");

                    b.Property<int>("Year")
                        .HasColumnType("int");

                    b.HasKey("LeaveBalanceID");

                    b.ToTable("LeaveBalances");
                });

            modelBuilder.Entity("EMS.Models.LeaveMaster", b =>
                {
                    b.Property<int>("LeaveMasterID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("LeaveMasterID"));

                    b.Property<int>("AnnualQuota")
                        .HasColumnType("int");

                    b.Property<bool>("IsCarryForward")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsEncashable")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LeaveType")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("MaxCarryForward")
                        .HasColumnType("int");

                    b.HasKey("LeaveMasterID");

                    b.ToTable("LeaveMasters");
                });

            modelBuilder.Entity("EMS.Models.LeaveTransaction", b =>
                {
                    b.Property<int>("LeaveTransactionID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("LeaveTransactionID"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("Days")
                        .HasColumnType("int");

                    b.Property<int>("EmpID")
                        .HasColumnType("int");

                    b.Property<DateTime>("LeaveDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("LeaveMasterID")
                        .HasColumnType("int");

                    b.Property<string>("TransactionType")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("LeaveTransactionID");

                    b.ToTable("LeaveTransactions");
                });

            modelBuilder.Entity("EMS.Models.LnA", b =>
                {
                    b.Property<int>("LnAID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("LnAID"));

                    b.Property<string>("Description")
                        .HasColumnType("longtext");

                    b.Property<int>("EmpID")
                        .HasColumnType("int");

                    b.Property<double?>("InterestRate")
                        .HasColumnType("double");

                    b.Property<bool?>("IsApproved")
                        .HasColumnType("tinyint(1)");

                    b.Property<int?>("IsApprovedBy")
                        .HasColumnType("int");

                    b.Property<double?>("LoanAmount")
                        .HasColumnType("double");

                    b.Property<double?>("MonthlyInstallment")
                        .HasColumnType("double");

                    b.Property<int?>("Period")
                        .HasColumnType("int");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime(6)");

                    b.HasKey("LnAID");

                    b.ToTable("LnAs");
                });

            modelBuilder.Entity("EMS.Models.LnABalance", b =>
                {
                    b.Property<int>("LnABalanceID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("LnABalanceID"));

                    b.Property<double?>("AmountRecovered")
                        .HasColumnType("double");

                    b.Property<double?>("ClosingBalance")
                        .HasColumnType("double");

                    b.Property<int>("EmpID")
                        .HasColumnType("int");

                    b.Property<int>("LnAID")
                        .HasColumnType("int");

                    b.Property<string>("Month")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<double?>("OpeningBalance")
                        .HasColumnType("double");

                    b.Property<int>("Year")
                        .HasColumnType("int");

                    b.HasKey("LnABalanceID");

                    b.ToTable("LnABalances");
                });

            modelBuilder.Entity("EMS.Models.LnATransaction", b =>
                {
                    b.Property<int>("LnATransactionID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("LnATransactionID"));

                    b.Property<double>("Amount")
                        .HasColumnType("double");

                    b.Property<string>("Description")
                        .HasColumnType("longtext");

                    b.Property<int>("LnAID")
                        .HasColumnType("int");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("TransactionType")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("LnATransactionID");

                    b.ToTable("LnATransactions");
                });

            modelBuilder.Entity("EMS.Models.Location", b =>
                {
                    b.Property<int>("LocationID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("LocationID"));

                    b.Property<string>("LocationName")
                        .HasColumnType("longtext");

                    b.HasKey("LocationID");

                    b.ToTable("Locations");
                });

            modelBuilder.Entity("EMS.Models.PayrollSlab", b =>
                {
                    b.Property<int>("SlabID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("SlabID"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<bool>("IsFlatBasic")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsPenalty")
                        .HasColumnType("tinyint(1)");

                    b.Property<int?>("MaxHours")
                        .HasColumnType("int");

                    b.Property<int>("MinHours")
                        .HasColumnType("int");

                    b.Property<int>("Multiplier")
                        .HasColumnType("int");

                    b.HasKey("SlabID");

                    b.ToTable("PayrollSlabs");
                });

            modelBuilder.Entity("EMS.Models.Position", b =>
                {
                    b.Property<int>("PositionID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("PositionID"));

                    b.Property<int>("DeptID")
                        .HasColumnType("int");

                    b.Property<string>("PositionName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("ReportsTo")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("PositionID");

                    b.ToTable("Positions");
                });

            modelBuilder.Entity("EMS.Models.Role", b =>
                {
                    b.Property<int>("RoleID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("RoleID"));

                    b.Property<string>("RoleName")
                        .HasColumnType("longtext");

                    b.HasKey("RoleID");

                    b.ToTable("Roles");
                });

            modelBuilder.Entity("EMS.Models.SalaryStructure", b =>
                {
                    b.Property<int>("SalaryStructureID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("SalaryStructureID"));

                    b.Property<double?>("AverageCTC")
                        .HasColumnType("double");

                    b.Property<double?>("MaxCTC")
                        .HasColumnType("double");

                    b.Property<double?>("MinCTC")
                        .HasColumnType("double");

                    b.Property<int>("PositionID")
                        .HasColumnType("int");

                    b.HasKey("SalaryStructureID");

                    b.ToTable("SalaryStructures");
                });

            modelBuilder.Entity("MonthlyPayrollTransaction", b =>
                {
                    b.Property<int>("MptID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("MptID"));

                    b.Property<decimal?>("BasicSalary")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DA")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("Deductions")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("EmpID")
                        .HasColumnType("int");

                    b.Property<decimal?>("HRA")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Month")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<decimal?>("NetPay")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("OtherAllowances")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("Year")
                        .HasColumnType("int");

                    b.HasKey("MptID");

                    b.ToTable("MonthlyPayrollTransactions");
                });
#pragma warning restore 612, 618
        }
    }
}
