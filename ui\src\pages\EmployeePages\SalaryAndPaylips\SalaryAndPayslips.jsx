import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Download, Eye, Calendar, DollarSign, FileText, Search, Filter, ChevronDown, CheckCircle, AlertCircle, User, Building } from 'lucide-react';

//components imports
import Payslips from "./components/Payslips";
import Salary from './components/Salary';
import SummaryCards from '@/pages/EmployeePages/SalaryAndPaylips/components/SummaryCards';
import Slip from '@/pages/EmployeePages/SalaryAndPaylips/components/Slip';

const SalaryAndPayslips = () => {
    const [selectedMonth, setSelectedMonth] = useState('2024-10');
    const [searchTerm, setSearchTerm] = useState('');
    const [previewData, setPreviewData] = useState(null);
    const [showPreview, setShowPreview] = useState(false);

    // Mock pay slip data for last 3 months
    const paySlipData = {
        '2024-10': {
            month: 'October 2024',
            payPeriod: 'Oct 01, 2024 - Oct 31, 2024',
            payDate: '2024-11-01',
            status: 'paid',
            employee: {
                id: 'EMP001',
                name: 'John Doe',
                designation: 'Senior Software Engineer',
                department: 'Technology',
                joinDate: '2022-01-15',
                bankAccount: '**** **** **** 1234'
            },
            earnings: {
                basicSalary: 50000,
                hra: 20000,
                conveyanceAllowance: 2000,
                medicalAllowance: 1500,
                performanceBonus: 5000,
                overtimePay: 3000
            },
            deductions: {
                providentFund: 6000,
                professionalTax: 200,
                incomeTax: 8500,
                insurance: 1200,
                lateDeduction: 500
            },
            workingDays: 22,
            presentDays: 20,
            leaves: 2,
            overtime: 12
        },
        '2024-9': {
            month: 'September 2024',
            payPeriod: 'Sep 01, 2024 - Sep 30, 2024',
            payDate: '2024-10-01',
            status: 'paid',
            employee: {
                id: 'EMP001',
                name: 'John Doe',
                designation: 'Senior Software Engineer',
                department: 'Technology',
                joinDate: '2022-01-15',
                bankAccount: '**** **** **** 1234'
            },
            earnings: {
                basicSalary: 50000,
                hra: 20000,
                conveyanceAllowance: 2000,
                medicalAllowance: 1500,
                performanceBonus: 3000,
                overtimePay: 4000
            },
            deductions: {
                providentFund: 6000,
                professionalTax: 200,
                incomeTax: 8200,
                insurance: 1200,
                lateDeduction: 0
            },
            workingDays: 21,
            presentDays: 21,
            leaves: 0,
            overtime: 16
        },
        '2024-8': {
            month: 'August 2024',
            payPeriod: 'Aug 01, 2024 - Aug 31, 2024',
            payDate: '2024-09-01',
            status: 'paid',
            employee: {
                id: 'EMP001',
                name: 'John Doe',
                designation: 'Senior Software Engineer',
                department: 'Technology',
                joinDate: '2022-01-15',
                bankAccount: '**** **** **** 1234'
            },
            earnings: {
                basicSalary: 50000,
                hra: 20000,
                conveyanceAllowance: 2000,
                medicalAllowance: 1500,
                performanceBonus: 4000,
                overtimePay: 2500
            },
            deductions: {
                providentFund: 6000,
                professionalTax: 200,
                incomeTax: 8800,
                insurance: 1200,
                lateDeduction: 300
            },
            workingDays: 22,
            presentDays: 19,
            leaves: 3,
            overtime: 10
        }
    };

    const months = [
        { value: '2024-10', label: 'October 2024' },
        { value: '2024-9', label: 'September 2024' },
        { value: '2024-8', label: 'August 2024' }
    ];

    const currentData = paySlipData[selectedMonth];

    const calculateTotals = (data) => {
        const totalEarnings = Object.values(data.earnings).reduce((sum, val) => sum + val, 0);
        const totalDeductions = Object.values(data.deductions).reduce((sum, val) => sum + val, 0);
        const netPay = totalEarnings - totalDeductions;
        return { totalEarnings, totalDeductions, netPay };
    };

    const { totalEarnings, totalDeductions, netPay } = calculateTotals(currentData);

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            minimumFractionDigits: 0
        }).format(amount);
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    };

    const handlePreview = (monthKey) => {
        setPreviewData(paySlipData[monthKey]);
        setShowPreview(true);
    };

    const handleDownload = (monthKey) => {
        // Simulate PDF download
        const data = paySlipData[monthKey];
        console.log(`Downloading PDF for ${data.month}`);
        // In real implementation, this would trigger PDF generation and download
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'paid':
                return 'bg-green-50 text-green-700 border-green-200';
            case 'pending':
                return 'bg-yellow-50 text-yellow-700 border-yellow-200';
            case 'processing':
                return 'bg-blue-50 text-blue-700 border-blue-200';
            default:
                return 'bg-gray-50 text-gray-700 border-gray-200';
        }
    };

    const filteredMonths = months.filter(month =>
        month.label.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return (
        // <div className=" bg-gray-50 transition-colors duration-300">
        //     <motion.div
        //         initial={{ opacity: 0, y: 20 }}
        //         animate={{ opacity: 1, y: 0 }}
        //         transition={{ duration: 0.6 }}
        //         className="container mx-auto px-3 py-4"
        //     >
        //         {/* Header */}
        //         <motion.div
        //             initial={{ opacity: 0 }}
        //             animate={{ opacity: 1 }}
        //             transition={{ delay: 0.2 }}
        //             className="mb-6 flex justify-between items-start"
        //         >
        //             <div>
        //                 <h1 className="text-2xl font-bold text-gray-800 mb-1">
        //                     Pay Slips
        //                 </h1>
        //                 <p className="text-gray-600 text-sm">
        //                     Download and view your salary slips for the last 3 months
        //                 </p>
        //             </div>
        //             <div className="flex items-center space-x-2">
        //                 <button className="flex items-center space-x-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-lg transition-colors text-sm">
        //                     <FileText className="h-4 w-4" />
        //                     <span>Tax Statement</span>
        //                 </button>
        //             </div>
        //         </motion.div>

        //         {/* Summary Cards */}
        //         <SummaryCards
        //             currentData={currentData}
        //             formatCurrency={formatCurrency}
        //             netPay={netPay}
        //             formatDate={formatDate}
        //             motion={motion}
        //             DollarSign={DollarSign}
        //             Calendar={Calendar}
        //             Building={Building}
        //             User={User}
        //         />


        //         {/* Main Content - Side by Side */}
        //         <div className=" grid grid-cols-1 lg:grid-cols-2 gap-6">

        //             {/* Left Side - Pay Slip List */}
        //             <Payslips
        //                 filteredMonths={filteredMonths}
        //                 paySlipData={paySlipData}
        //                 setSelectedMonth={setSelectedMonth}
        //                 handlePreview={handlePreview}
        //                 handleDownload={handleDownload}
        //                 motion={motion}
        //                 calculateTotals={calculateTotals}
        //                 getStatusColor={getStatusColor}
        //                 CheckCircle={CheckCircle}
        //                 formatCurrency={formatCurrency}
        //                 formatDate={formatDate}
        //                 Eye={Eye}
        //                 Download={Download}
        //                 FileText={FileText}
        //             />

        //             {/* Right Side - Pay Slip Preview */}
        //             <Salary
        //                 currentData={currentData}
        //                 formatCurrency={formatCurrency}
        //                 formatDate={formatDate}
        //                 motion={motion}
        //                 totalEarnings={totalEarnings}
        //                 totalDeductions={totalDeductions}
        //                 netPay={netPay}
        //             />
        //         </div>
        //     </motion.div>
        // </div>
        <Slip/>
    );
};

export default SalaryAndPayslips;
