﻿using EMS.Data;
using EMS.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace EMS.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AttendancesController : ControllerBase
    {
        private readonly EMSDbContext _context;

        public AttendancesController(EMSDbContext context)
        {
            _context = context;
        }

        // GET: api/Attendances
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Attendance>>> GetAttendances()
        {
            return await _context.Attendances.ToListAsync();
        }

        // GET: api/Attendances/5
        [HttpGet("{id}")]
        public async Task<ActionResult<Attendance>> GetAttendance(int id)
        {
            var attendance = await _context.Attendances.FindAsync(id);

            if (attendance == null)
            {
                return NotFound();
            }

            return attendance;
        }

        // PUT: api/Attendances/5
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPut("{id}")]
        public async Task<IActionResult> PutAttendance(int id, Attendance attendance)
        {
            if (id != attendance.AttendaceID)
            {
                return BadRequest();
            }

            _context.Entry(attendance).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!AttendanceExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/Attendances
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPost]
        public async Task<ActionResult<Attendance>> PostAttendance(List<Attendance> attendances)
        {
            try
            {
                foreach (var attendance in attendances)
                {
                    if (attendance == null)
                    {
                        return BadRequest("One or more attendance records are null.");
                    }

                    if (attendance.CheckInTime.HasValue && attendance.CheckOutTime.HasValue)
                    {
                        attendance.TotalHours = attendance.CheckOutTime.Value - attendance.CheckInTime.Value;
                    }
                    else
                    {
                        attendance.TotalHours = null;
                    }

                    _context.Attendances.Add(attendance);
                }
                await _context.SaveChangesAsync();
            }
            catch(Exception ex)
            {
                return BadRequest($"An error occurred while processing the attendance records: {ex.Message}");
            }

            return Ok("Import Successfull");
        }

        // DELETE: api/Attendances/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteAttendance(int id)
        {
            var attendance = await _context.Attendances.FindAsync(id);
            if (attendance == null)
            {
                return NotFound();
            }

            _context.Attendances.Remove(attendance);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool AttendanceExists(int id)
        {
            return _context.Attendances.Any(e => e.AttendaceID == id);
        }


        [HttpPost("ImportAttendance")]
        public IActionResult ImportAttendance(IFormFile file)
        {
            if (file == null || file.Length == 0)
                return BadRequest(new { Message = "No file uploaded or file is empty." });

            try
            {
                IWorkbook workbook;
                using (var stream = file.OpenReadStream())
                {
                    var ext = Path.GetExtension(file.FileName);
                    if (ext.Equals(".xls", StringComparison.OrdinalIgnoreCase))
                        workbook = new HSSFWorkbook(stream);
                    else
                        workbook = new XSSFWorkbook(stream);
                }

                if (workbook.NumberOfSheets <= 0)
                    return BadRequest(new { Message = "The workbook contains no worksheets." });

                var attendances = new List<Attendance>();

                // Loop through all sheets
                for (int sheetIndex = 0; sheetIndex < workbook.NumberOfSheets; sheetIndex++)
                {
                    var sheet = workbook.GetSheetAt(sheetIndex);
                    if (sheet == null) continue;

                    int currentEmpId = 0; // Track currently active Emp ID

                    // Loop through all rows in the sheet
                    for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
                    {
                        var row = sheet.GetRow(rowIndex);
                        if (row == null) continue;

                        // Check if this row contains an Emp Code in column 3 (index 2)
                        var empCodeCell = row.GetCell(2);
                        if (empCodeCell != null && int.TryParse(empCodeCell.ToString().Trim(), out int empIdCandidate))
                        {
                            currentEmpId = empIdCandidate; // Found new employee code
                            continue; // Skip the row, attendance rows follow
                        }

                        // Skip until we have a valid employee ID
                        if (currentEmpId == 0) continue;

                        // Try to read date cell from column index 1
                        var dateCell = row.GetCell(1);
                        var dateValue = TryGetDate(dateCell);
                        if (!dateValue.HasValue) continue; // skip rows without a valid date

                        // Extract attendance times
                        var checkIn = TryGetTime(row.GetCell(2)); // InTime
                        var checkOut = TryGetTime(row.GetCell(3)); // OutTime

                        var attendance = new Attendance
                        {
                            EmpID = currentEmpId,
                            Date = dateValue.Value,
                            CheckInTime = checkIn,
                            CheckOutTime = checkOut
                        };

                        // Calculate total hours if both times are valid
                        if (attendance.CheckInTime.HasValue && attendance.CheckOutTime.HasValue)
                        {
                            attendance.TotalHours = attendance.CheckOutTime.Value.ToTimeSpan()
                                                   - attendance.CheckInTime.Value.ToTimeSpan();
                        }

                        attendances.Add(attendance);
                    }
                }

                _context.Attendances.AddRange(attendances);
                _context.SaveChanges();

                return Ok(new { Message = "Import successful", Count = attendances.Count });
            }
            catch (ArgumentException ex) when (ex.Message.Contains("Sheet index"))
            {
                return BadRequest(new { Message = "The workbook has no accessible sheets or the first sheet is missing." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Failed to import attendance.", Error = ex.Message });
            }
        }

        [HttpGet("GetEmployeesWithAttendance/{year:int}/{month:int}")]
        public async Task<ActionResult<IEnumerable<object>>> GetEmployeesWithAttendance(int year, int month)
        {
            var attendances = await (from a in _context.Attendances
                                     join e in _context.Employees on a.EmpID equals e.EmpID
                                     where a.Date.HasValue &&
                                           a.Date.Value.Year == year &&
                                           a.Date.Value.Month == month
                                     select new
                                     {
                                         a.AttendaceID,
                                         a.EmpID,
                                         e.FirstName,
                                         e.LastName,
                                         a.Date,
                                         a.CheckInTime,
                                         a.CheckOutTime,
                                         a.TotalHours
                                     })
                                     .ToListAsync();

            if (!attendances.Any())
            {
                return NotFound($"No attendance records found for {month}/{year}.");
            }

            var result = attendances
                .GroupBy(a => new { a.EmpID, a.FirstName, a.LastName })
                .Select(g =>
                {
                    // Sum all nullable TimeSpan values safely
                    var totalTicks = g.Sum(x => x.TotalHours.HasValue ? x.TotalHours.Value.Ticks : 0);

                    var totalTime = new TimeSpan(totalTicks);

                    return new
                    {
                        g.Key.EmpID,
                        g.Key.FirstName,
                        g.Key.LastName,
                        AttendanceRecords = g.Select(x => new
                        {
                            x.AttendaceID,
                            x.Date,
                            x.CheckInTime,
                            x.CheckOutTime,
                            x.TotalHours
                        }).ToList(),
                        // Show full total hours (not resetting after 24h)
                        TotalHoursWorked = $"{(int)totalTime.TotalHours:D2}:{totalTime.Minutes:D2}:{totalTime.Seconds:D2}"
                    };
                }).OrderBy(g => g.EmpID)
                .ToList();

            return Ok(result);
        }

        [HttpPost("LeaveRegularization")]
        public async Task<ActionResult> LeaveRegularization()
        {
            var employees = await _context.Employees.ToListAsync();

            foreach (var emp in employees)
            {
                // Get approved Paid/Sick leaves
                var leaves = await _context.Leaves
                    .Where(l => l.EmpID == emp.EmpID && l.IsApproved == true
                                && (l.LeaveType == "Paid" || l.LeaveType == "Sick"))
                    .ToListAsync();

                foreach (var leave in leaves)
                {
                    DateTime startDate;
                    DateTime endDate;

                    if (leave.StartDate == null && leave.EndDate != null)
                    {
                        // Single-day leave (use EndDate)
                        startDate = leave.EndDate.Value.Date;
                        endDate = leave.EndDate.Value.Date;
                    }
                    else if (leave.StartDate != null && leave.EndDate != null)
                    {
                        // Multi-day leave (range between StartDate and EndDate)
                        startDate = leave.StartDate.Value.Date;
                        endDate = leave.EndDate.Value.Date;
                    }
                    else
                    {
                        // Invalid case, skip
                        continue;
                    }

                    for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
                    {
                        // Check if attendance already exists for this employee/date
                        var existingAttendance = await _context.Attendances
                            .FirstOrDefaultAsync(a => a.EmpID == emp.EmpID && a.Date == date);

                        if (existingAttendance == null)
                        {
                            // Insert new leave attendance
                            var attendance = new Attendance
                            {
                                EmpID = emp.EmpID,
                                Date = date,
                                CheckInTime = new TimeOnly(10, 0),
                                CheckOutTime = new TimeOnly(18, 0),
                                TotalHours = TimeSpan.FromHours(8)
                            };
                            _context.Attendances.Add(attendance);
                        }
                        else
                        {
                            // Update existing to mark as leave
                            existingAttendance.CheckInTime = new TimeOnly(10, 0);
                            existingAttendance.CheckOutTime = new TimeOnly(18, 0);
                            existingAttendance.TotalHours = TimeSpan.FromHours(8);
                            _context.Attendances.Update(existingAttendance);
                        }
                    }
                }
            }

            await _context.SaveChangesAsync();
            return Ok("Leave regularization completed successfully.");
        }


        // Helper method for date extraction
        private DateTime? TryGetDate(ICell cell)
        {
            if (cell == null) return null;
            if (cell.CellType == CellType.Numeric && DateUtil.IsCellDateFormatted(cell))
                return cell.DateCellValue;
            if (DateTime.TryParse(cell.ToString(), out DateTime parsedDate))
                return parsedDate;
            return null;
        }

        // Helper method for time extraction
        private TimeOnly? TryGetTime(ICell cell)
        {
            if (cell == null) return null;

            if (cell.CellType == CellType.Numeric && DateUtil.IsCellDateFormatted(cell))
            {
                var dateTimeValue = cell.DateCellValue; // This is DateTime?
                if (dateTimeValue.HasValue)
                    return TimeOnly.FromDateTime(dateTimeValue.Value);
                else
                    return null;
            }

            if (DateTime.TryParse(cell.ToString(), out DateTime parsed))
                return TimeOnly.FromDateTime(parsed);

            return null;
        }










      








    }
}
