﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EMS.Data;
using EMS.Models;
using EMS.Models.NonDBModels;
using EMS.Services;
using System.Linq.Expressions;
using System.Reflection;

namespace EMS.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmployeesController : ControllerBase
    {
        private readonly EMSDbContext _context;
        private readonly IConfiguration _configuration;

        public EmployeesController(EMSDbContext context, IConfiguration configuration)
        {
            _context = context;
            _configuration = configuration;
        }

        // GET: api/Employees
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Employee>>> GetEmployees()
        {
            return await _context.Employees.ToListAsync();
        }

        // GET: api/Employees/5
        [HttpGet("{id}")]
        public async Task<ActionResult<Employee>> GetEmployee(int id)
        {
            var employee = await _context.Employees.FindAsync(id);

            if (employee == null)
            {
                return NotFound();
            }

            return employee;
        }

        [HttpPost("Search")]
        public async Task<ActionResult<PaginatedResponse<Employee>>> GetEmployeesWithSearch([FromBody] EmployeeSearchRequest request)
        {
            if (request.PageNumber <= 0) request.PageNumber = 1;
            if (request.PageSize <= 0) request.PageSize = 10;

            var queryable = _context.Employees.AsQueryable();

            // 🔍 Global search
            if (!string.IsNullOrWhiteSpace(request.Query))
            {
                var term = request.Query.ToLower();
                queryable = queryable.Where(e =>
                    (e.FirstName != null && e.FirstName.ToLower().Contains(term)) ||
                    (e.LastName != null && e.LastName.ToLower().Contains(term)) ||
                    (e.PersonalEmail != null && e.PersonalEmail.ToLower().Contains(term)) ||
                    (e.WorkEmail != null && e.WorkEmail.ToLower().Contains(term)) ||
                    (e.PhoneNumber != null && e.PhoneNumber.ToLower().Contains(term)) ||
                    (e.Address != null && e.Address.ToLower().Contains(term)) ||
                    (e.EmergencyContact != null && e.EmergencyContact.ToLower().Contains(term)) ||
                    (e.EmergencyContactNumber != null && e.EmergencyContactNumber.ToLower().Contains(term)) ||
                    (e.Gender != null && e.Gender.ToLower().Contains(term))
                );
            }

            // 🎯 Column-specific filters
            if (!string.IsNullOrWhiteSpace(request.FirstName))
                queryable = queryable.Where(e => e.FirstName != null && e.FirstName.ToLower().Contains(request.FirstName.ToLower()));

            if (!string.IsNullOrWhiteSpace(request.LastName))
                queryable = queryable.Where(e => e.LastName != null && e.LastName.ToLower().Contains(request.LastName.ToLower()));

            if (!string.IsNullOrWhiteSpace(request.PersonalEmail))
                queryable = queryable.Where(e => e.PersonalEmail != null && e.PersonalEmail.ToLower().Contains(request.PersonalEmail.ToLower()));

            if (!string.IsNullOrWhiteSpace(request.WorkEmail))
                queryable = queryable.Where(e => e.WorkEmail != null && e.WorkEmail.ToLower().Contains(request.WorkEmail.ToLower()));

            if (!string.IsNullOrWhiteSpace(request.PhoneNumber))
                queryable = queryable.Where(e => e.PhoneNumber != null && e.PhoneNumber.ToLower().Contains(request.PhoneNumber.ToLower()));

            if (!string.IsNullOrWhiteSpace(request.Address))
                queryable = queryable.Where(e => e.Address != null && e.Address.ToLower().Contains(request.Address.ToLower()));

            if (!string.IsNullOrWhiteSpace(request.EmergencyContact))
                queryable = queryable.Where(e => e.EmergencyContact != null && e.EmergencyContact.ToLower().Contains(request.EmergencyContact.ToLower()));

            if (!string.IsNullOrWhiteSpace(request.EmergencyContactNumber))
                queryable = queryable.Where(e => e.EmergencyContactNumber != null && e.EmergencyContactNumber.ToLower().Contains(request.EmergencyContactNumber.ToLower()));

            if (!string.IsNullOrWhiteSpace(request.Gender))
                queryable = queryable.Where(e => e.Gender != null && e.Gender.ToLower().Contains(request.Gender.ToLower()));

            if (request.OBStatus != null)
                queryable = queryable.Where(e => e.OBStatus == request.OBStatus);

            if (request.isActive != null)
                queryable = queryable.Where(e => e.IsActive == request.isActive);

            // 📄 Pagination
            int totalCount = await queryable.CountAsync();
            int totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);

            if (!string.IsNullOrWhiteSpace(request.SortField))
            {
                bool ascending = request.SortOrder.ToLower() == "asc";
                queryable = ApplySorting(queryable, request.SortField, ascending);
            }
            else
            {
                // Default sorting if no field is specified
                queryable = queryable.OrderBy(e => e.EmpID);
            }

            var employees = await queryable
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync();

            var response = new PaginatedResponse<Employee>
            {
                Data = employees,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize,
                TotalPages = totalPages
            };

            return Ok(response);
        }


        // PUT: api/Employees/5
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPut("{id}")]
        public async Task<IActionResult> PutEmployee(int id, Employee employee)
        {
            if (id != employee.EmpID)
            {
                return BadRequest();
            }

            _context.Entry(employee).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EmployeeExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/Employees
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754

        [HttpPost("Import")]
        public async Task<ActionResult<Employee>> PostEmployeeImport(List<EmployeeDTO> employees)
        {
            try
            {
                foreach (var employee in employees)
                {
                    if (employee == null)
                    {
                        return BadRequest("One or more employee data is null.");
                    }
                    if (employee == null)
                    {
                        return BadRequest("Employee data is null.");
                    }
                    Employee employee1 = new Employee()
                    {
                        FirstName = employee.FirstName,
                        LastName = employee.LastName,
                        PersonalEmail = employee.PersonalEmail,
                        WorkEmail = employee.WorkEmail,
                        PhoneNumber = employee.PhoneNumber,
                        Address = employee.Address,
                        EmergencyContact = employee.EmergencyContact,
                        EmergencyContactNumber = employee.EmergencyContactNumber,
                        DateOfBirth = employee.DateOfBirth,
                        Gender = employee.Gender,
                        DateOfJoining = employee.DateOfJoining,
                        OBStatus = false,
                        IsActive = true,
                        ProfilePicture = employee.ProfilePicture,
                        RoleID = employee.RoleID
                    };
                    _context.Employees.Add(employee1);
                    await _context.SaveChangesAsync();

                    if (employee.EmpTypeID == null)
                    {
                        employee.EmpTypeID = 1; // Default to Full-Time if not specified
                    }

                    EmpDeptPos empDeptPos = new EmpDeptPos()
                    {
                        EmpID = employee1.EmpID,
                        DeptID = employee.DeptID,
                        PositionID = employee.PositionID,
                        EmpTypeID = employee.EmpTypeID
                    };
                    _context.EmpDeptPositions.Add(empDeptPos);
                    await _context.SaveChangesAsync();

                    string DeptName = _context.Departments
                        .Where(d => d.DeptID == employee.DeptID)
                        .Select(d => d.DeptName)
                        .FirstOrDefault();

                    if (DeptName != null)
                    {
                        DeptName = DeptName.Substring(0, Math.Min(3, DeptName.Length)).ToUpper();
                    }
                    else
                    {
                        DeptName = string.Empty; // Handle case where DeptName is null
                    }
                    string Username = DeptName
    + employee.FirstName.Substring(0, Math.Min(3, employee.FirstName.Length)).ToUpper()
    + employee1.EmpID.ToString("D4");
                    string Password = PasswordGenerate.GeneratePassword();

                    EmpCred empCred = new EmpCred()
                    {
                        EmpID = employee1.EmpID,
                        UserName = Username,
                        Password = Password
                    };
                    _context.EmpCreds.Add(empCred);
                    await _context.SaveChangesAsync();

                    /*if ((!string.IsNullOrEmpty(employee.WorkEmail) || !string.IsNullOrEmpty(employee.PersonalEmail)) && !string.IsNullOrEmpty(Username) && !string.IsNullOrEmpty(Password))
                    {
                        if (!string.IsNullOrEmpty(employee.WorkEmail))
                        {
                            EmailService emailService = new EmailService(_context, _configuration);
                            string subject = "Welcome to the EMS";
                            string result = emailService.SendLoginCredentialEmail(employee.WorkEmail, subject, Username, Password);
                        }
                        else
                        {
                            EmailService emailService = new EmailService(_context, _configuration);
                            string subject = "Welcome to the EMS";
                            string result = emailService.SendLoginCredentialEmail(employee.PersonalEmail, subject, Username, Password);
                        }
                    }
                    else
                    {
                        return BadRequest("Email, Username or Password is missing for one or more employees.");
                    }*/

                }
            }
            catch (Exception ex)
            {
                return BadRequest("An error occurred while processing the request: " + ex.Message);
            }


            // Return the created employee directly with 201 status
            return Ok("Import Completed");
        }

        [HttpGet("GetEmptyFieldsByEmpID/{EmpID}")]
        public async Task<ActionResult<object>> GetEmptyFieldsbyEmpID(int EmpID)
        {
            var result = new Dictionary<string, object>();

            // Helper to get empty fields for an entity
            List<string> GetEmptyFields<T>(T record)
            {
                var emptyFields = new List<string>();
                foreach (var prop in typeof(T).GetProperties())
                {
                    var value = record != null ? prop.GetValue(record) : null;
                    if (value == null || (value is string str && string.IsNullOrWhiteSpace(str)))
                    {
                        emptyFields.Add(prop.Name);
                    }
                }
                return emptyFields;
            }

            // Employee Table
            var employee = await _context.Employees.FindAsync(EmpID);
            result["Employee"] = GetEmptyFields(employee);

            // Bank Details
            var bankDetails = await _context.EmpBankDetails.FirstOrDefaultAsync(b => b.EmpID == EmpID);
            result["EmpBankDetails"] = GetEmptyFields(bankDetails);

            // Academic Table
            var academics = await _context.EmpAcadDetails.Where(a => a.EmpID == EmpID).ToListAsync();
            if (academics.Any())
            {
                result["Academics"] = academics
                    .Select(a => new { AcademicId = a.EadID, Fields = GetEmptyFields(a) })
                    .Where(a => a.Fields.Any())
                    .ToList();
            }
            else
            {
                result["Academics"] = GetEmptyFields<EmpAcadDetail>(null);
            }

            // Professional History
            var histories = await _context.EmpProfDetails.Where(h => h.EmpID == EmpID).ToListAsync();
            if (histories.Any())
            {
                result["ProfessionalHistory"] = histories
                    .Select(h => new { HistoryId = h.EpdID, Fields = GetEmptyFields(h) })
                    .Where(h => h.Fields.Any())
                    .ToList();
            }
            else
            {
                result["ProfessionalHistory"] = GetEmptyFields<EmpProfDetail>(null);
            }

            // ---- DOCUMENTS SECTION WITH MASTER LIST ----
            var masterDocs = await _context.Docs.ToListAsync(); // master list of required docs
            var empDocs = await _context.EmpDocs.Where(d => d.EmpId == EmpID).ToListAsync();
            var docEmptyList = new List<object>();

            foreach (var masterDoc in masterDocs)
            {
                var empDoc = empDocs.FirstOrDefault(e => e.DocName == masterDoc.DocName);

                // Prepare the list of empty fields
                var emptyFields = new List<string>();

                if (empDoc == null)
                {
                    // No document uploaded, mark all fields as empty
                    foreach (var prop in typeof(EmpDoc).GetProperties())
                    {
                        // Apply the special rule for DocIDNumber
                        if (prop.Name == "DocIDNumber" && !masterDoc.isDocIDNumRequired)
                            continue;

                        emptyFields.Add(prop.Name);
                    }

                    docEmptyList.Add(new
                    {
                        DocumentName = masterDoc.DocName,
                        Status = "Missing",
                        Fields = emptyFields
                    });
                }
                else
                {
                    // Document exists, check for empty fields
                    foreach (var prop in typeof(EmpDoc).GetProperties())
                    {
                        var value = prop.GetValue(empDoc);

                        // Apply the special rule for DocIDNumber
                        if (prop.Name == "DocIDNumber" && !masterDoc.isDocIDNumRequired)
                            continue;

                        if (value == null || (value is string str && string.IsNullOrWhiteSpace(str)))
                        {
                            emptyFields.Add(prop.Name);
                        }
                    }

                    if (emptyFields.Any())
                    {
                        docEmptyList.Add(new
                        {
                            DocumentName = empDoc.DocName,
                            Status = "Incomplete",
                            Fields = emptyFields
                        });
                    }
                    else
                    {
                        // Optional: mark it as complete
                        docEmptyList.Add(new
                        {
                            DocumentName = empDoc.DocName,
                            Status = "Complete",
                            Fields = new List<string>()
                        });
                    }
                }
            }

            result["Documents"] = new
            {
                TotalRequired = masterDocs.Count,
                TotalSubmitted = empDocs.Count,
                MissingCount = masterDocs.Count - empDocs.Count,
                Details = docEmptyList
            };

            return Ok(result);
        }





        [HttpPost]
        public async Task<ActionResult<Employee>> PostEmployee(EmployeeDTO employee)
        {
            if (employee == null)
            {
                return BadRequest("Employee data is null.");
            }
            Employee employee1 = new Employee()
            {
                FirstName = employee.FirstName,
                LastName = employee.LastName,
                PersonalEmail = employee.PersonalEmail,
                WorkEmail = employee.WorkEmail,
                PhoneNumber = employee.PhoneNumber,
                Address = employee.Address,
                EmergencyContact = employee.EmergencyContact,
                EmergencyContactNumber = employee.EmergencyContactNumber,
                DateOfBirth = employee.DateOfBirth,
                Gender = employee.Gender,
                DateOfJoining = employee.DateOfJoining,
                OBStatus = employee.OBStatus,
                IsActive = employee.IsActive,
                ProfilePicture = employee.ProfilePicture,
                RoleID = employee.RoleID,
                LocationID = employee.LocationID
            };
            _context.Employees.Add(employee1);
            await _context.SaveChangesAsync();

            if (employee.EmpTypeID == null)
            {
                employee.EmpTypeID = 1; // Default to Full-Time if not specified
            }

            EmpDeptPos empDeptPos = new EmpDeptPos()
            {
                EmpID = employee1.EmpID,
                DeptID = employee.DeptID,
                PositionID = employee.PositionID,
                EmpTypeID = employee.EmpTypeID
            };
            _context.EmpDeptPositions.Add(empDeptPos);
            await _context.SaveChangesAsync();

            string DeptName = _context.Departments
                .Where(d => d.DeptID == employee.DeptID)
                .Select(d => d.DeptName)
                .FirstOrDefault();

            if (DeptName != null)
            {
                DeptName = DeptName.Substring(0, Math.Min(3, DeptName.Length)).ToUpper();
            }
            else
            {
                DeptName = string.Empty; // Handle case where DeptName is null
            }
            string Username = DeptName + employee.FirstName.Substring(0, 4).ToUpper() + employee1.EmpID.ToString();
            string Password = PasswordGenerate.GeneratePassword();

            EmpCred empCred = new EmpCred()
            {
                EmpID = employee1.EmpID,
                UserName = Username,
                Password = Password
            };
            _context.EmpCreds.Add(empCred);
            await _context.SaveChangesAsync();

            /*if ((!string.IsNullOrEmpty(employee.WorkEmail) || !string.IsNullOrEmpty(employee.PersonalEmail)) && !string.IsNullOrEmpty(Username) && !string.IsNullOrEmpty(Password))
            {
                if (!string.IsNullOrEmpty(employee.WorkEmail))
                {
                    EmailService emailService = new EmailService(_context, _configuration);
                    string subject = "Welcome to the EMS";
                    string result = emailService.SendLoginCredentialEmail(employee.WorkEmail, subject, Username, Password);
                }
                else
                {
                    EmailService emailService = new EmailService(_context, _configuration);
                    string subject = "Welcome to the EMS";
                    string result = emailService.SendLoginCredentialEmail(employee.PersonalEmail, subject, Username, Password);
                }
            }
            else
            {
                return BadRequest("Email, Username or Password is missing for one or more employees.");
            }*/

            // Return the created employee directly with 201 status
            return StatusCode(201, employee1);
        }


        [HttpPost]
        [Route("UploadProfilePicture")]
        public async Task<IActionResult> UploadProfilePicture([FromForm] ProfilePictureDTO profilePictureDTO)
        {
            if (profilePictureDTO == null || profilePictureDTO.ProfilePicture == null)
            {
                return BadRequest("Invalid profile picture data.");
            }

            
            var employee = await _context.Employees.FindAsync(profilePictureDTO.EmpID);
            var employee1 = await _context.EmpCreds.FindAsync(profilePictureDTO.EmpID);
            if (employee == null)
            {
                return NotFound("Employee not found.");
            }

            // Save the profile picture to the server
            var uploadsFolderPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "ProfilePictures");
            if (!Directory.Exists(uploadsFolderPath))
            {
                Directory.CreateDirectory(uploadsFolderPath);
            }

            var fileName = $"{employee1.UserName}_PP.{profilePictureDTO.ProfilePicture.FileName.Split('.')[1]}";
            var filePath = Path.Combine(uploadsFolderPath, fileName);

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await profilePictureDTO.ProfilePicture.CopyToAsync(stream);
            }

            // Update the employee's profile picture path
            employee.ProfilePicture = $"/ProfilePictures/{fileName}";
            _context.Entry(employee).State = EntityState.Modified;
            await _context.SaveChangesAsync();

            return Ok(new { message = "Profile picture uploaded successfully.", filePath = employee.ProfilePicture });
        }

        // DELETE: api/Employees/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEmployee(int id)
        {
            var employee = await _context.Employees.FindAsync(id);
            if (employee == null)
            {
                return NotFound();
            }

            _context.Employees.Remove(employee);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool EmployeeExists(int id)
        {
            return _context.Employees.Any(e => e.EmpID == id);
        }

        public static IQueryable<T> ApplySorting<T>(IQueryable<T> source, string sortField, bool ascending)
        {
            var parameter = Expression.Parameter(typeof(T), "x");
            var property = typeof(T).GetProperty(sortField, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

            if (property == null)
                return source; // If invalid field, return unsorted

            var propertyAccess = Expression.MakeMemberAccess(parameter, property);
            var orderByExp = Expression.Lambda(propertyAccess, parameter);

            string methodName = ascending ? "OrderBy" : "OrderByDescending";

            var resultExp = Expression.Call(typeof(Queryable), methodName,
                new Type[] { typeof(T), property.PropertyType },
                source.Expression, Expression.Quote(orderByExp));

            return source.Provider.CreateQuery<T>(resultExp);
        }
    }

    public class EmployeeSearchRequest
    {
        public string? Query { get; set; }

        // Column-specific filters
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? PersonalEmail { get; set; }
        public string? WorkEmail { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Address { get; set; }
        public string? EmergencyContact { get; set; }
        public string? EmergencyContactNumber { get; set; }
        public string? Gender { get; set; }

        public bool? OBStatus { get; set; }
        public bool? isActive { get; set; }

        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SortField { get; set; }
        public string? SortOrder { get; set; } = "asc"; // Default to ascending order
    }


    public class ProfilePictureDTO
    {
        public int EmpID { get; set; }
        public IFormFile ProfilePicture { get; set; }
    }


    public class PaginatedResponse<T>
    {
        public IEnumerable<T> Data { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }
}
