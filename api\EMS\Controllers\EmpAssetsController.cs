﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EMS.Data;
using EMS.Models;

namespace EMS.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmpAssetsController : ControllerBase
    {
        private readonly EMSDbContext _context;

        public EmpAssetsController(EMSDbContext context)
        {
            _context = context;
        }

        // GET: api/EmpAssets
        [HttpGet]
        public async Task<ActionResult<IEnumerable<EmpAssets>>> GetEmpAssets()
        {
            return await _context.EmpAssets.ToListAsync();
        }

        // GET: api/EmpAssets/5
        [HttpGet("{id}")]
        public async Task<ActionResult<EmpAssets>> GetEmpAssets(int id)
        {
            var empAssets = await _context.EmpAssets.FindAsync(id);

            if (empAssets == null)
            {
                return NotFound();
            }

            return empAssets;
        }

        // PUT: api/EmpAssets/5
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPut("{id}")]
        public async Task<IActionResult> PutEmpAssets(int id, EmpAssets empAssets)
        {
            if (id != empAssets.EmpAssetID)
            {
                return BadRequest();
            }

            _context.Entry(empAssets).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EmpAssetsExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/EmpAssets
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPost]
        public async Task<ActionResult<EmpAssets>> PostEmpAssets(EmpAssets empAssets)
        {
            _context.EmpAssets.Add(empAssets);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetEmpAssets", new { id = empAssets.EmpAssetID }, empAssets);
        }

        // DELETE: api/EmpAssets/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEmpAssets(int id)
        {
            var empAssets = await _context.EmpAssets.FindAsync(id);
            if (empAssets == null)
            {
                return NotFound();
            }

            _context.EmpAssets.Remove(empAssets);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool EmpAssetsExists(int id)
        {
            return _context.EmpAssets.Any(e => e.EmpAssetID == id);
        }
    }
}
